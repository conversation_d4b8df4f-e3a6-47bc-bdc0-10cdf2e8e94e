#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules/touch/bin/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules/touch/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules/touch/bin/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules/touch/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/touch@3.1.1/node_modules:/Users/<USER>/mine/webrtc-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../touch/bin/nodetouch.js" "$@"
else
  exec node  "$basedir/../touch/bin/nodetouch.js" "$@"
fi
