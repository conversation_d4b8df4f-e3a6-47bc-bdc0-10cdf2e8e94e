hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  chokidar@3.6.0:
    chokidar: private
  concat-map@0.0.1:
    concat-map: private
  create-require@1.1.1:
    create-require: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  diff@4.0.2:
    diff: private
  fill-range@7.1.1:
    fill-range: private
  fsevents@2.3.3:
    fsevents: private
  glob-parent@5.1.2:
    glob-parent: private
  has-flag@3.0.0:
    has-flag: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  make-error@1.3.6:
    make-error: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  normalize-path@3.0.0:
    normalize-path: private
  picomatch@2.3.1:
    picomatch: private
  pstree.remy@1.1.8:
    pstree.remy: private
  readdirp@3.6.0:
    readdirp: private
  semver@7.7.2:
    semver: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.10.0:
    undici-types: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  yn@3.1.1:
    yn: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Mon, 01 Sep 2025 09:12:38 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
