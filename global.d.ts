import { WebSocket } from "ws";

declare global {
  type MsgDataType = {
    type: "connect" | "call" | "answer" | "getAllClients" | "getHostInfo";
    data: {
      sdp: number;
      sessionId: string;
    };
  };

  type People = Record<
    string,
    {
      sessionId: string;
      ws: WebSocket;
      hostInfo?: {
        remoteAddress: string;
        remotePort: number;
        userAgent?: string;
        origin?: string;
        connectedAt: Date;
      };
    }
  >;
}

export {};
