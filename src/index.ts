import WebSocket from "ws";
const WebSocketServer = WebSocket.Server;
const wss = new WebSocketServer({ port: 3002 });

function createId() {
  let e = () =>
    Math.floor((1 + Math.random()) * 65536)
      .toString(16)
      .substring(1);

  return `${e()}${e()}-${e()}-${e()}-${e()}-${e()}${e()}`;
}

//  存储连接的客户端
const people: People = {};

wss.on("connection", (ws, req) => {
  // 收集host信息
  const hostInfo = {
    remoteAddress: req.socket.remoteAddress || 'unknown',
    remotePort: req.socket.remotePort || 0,
    userAgent: req.headers['user-agent'],
    origin: req.headers.origin,
    connectedAt: new Date()
  };

  console.log('新连接的Host信息:', {
    remoteAddress: hostInfo.remoteAddress,
    remotePort: hostInfo.remotePort,
    userAgent: hostInfo.userAgent,
    origin: hostInfo.origin,
    connectedAt: hostInfo.connectedAt.toISOString()
  });

  ws.on("message", (message) => {
    const msg: MsgDataType = JSON.parse(message.toString());

    switch (msg.type) {
      case "connect":
        // 将连接的客户端存起来
        const sessionId = createId();
        people[sessionId] = {
          sessionId,
          ws,
          hostInfo
        };

        console.log(`客户端 ${sessionId} 已连接，Host信息已保存`);
        ws.send(JSON.stringify({ type: "content", data: sessionId }));
        break;
      case "call":
        // 将 sdp 发给接收端，sessionId 为 接收端的 id
        const sdp = msg.data.sdp;
        const sId = msg.data.sessionId;

        if (people[sId]) {
          people[sId].ws.send(JSON.stringify({ type: "call", data: sdp }));
        }
        break;
      case "answer":
        // 接收端将 sdp 发给发起端，sessionId 为 发起端的 id
        const answerSDP = msg.data.sdp;
        const recevId = msg.data.sessionId;

        if (people[recevId]) {
          people[recevId].ws.send(
            JSON.stringify({ type: "answer", data: answerSDP })
          );
        }
        break;
      case "getAllClients":
        ws.send(
          JSON.stringify({
            type: "getAllClients",
            data: Object.keys(people),
          })
        );
        break;
      case "getHostInfo":
        // 获取指定客户端的host信息，如果没有指定sessionId则返回所有客户端的host信息
        const targetSessionId = msg.data?.sessionId;

        if (targetSessionId && people[targetSessionId]) {
          // 返回指定客户端的host信息
          const clientInfo = people[targetSessionId];
          console.log(`获取客户端 ${targetSessionId} 的Host信息:`, clientInfo.hostInfo);
          ws.send(
            JSON.stringify({
              type: "hostInfo",
              data: {
                sessionId: targetSessionId,
                hostInfo: clientInfo.hostInfo
              }
            })
          );
        } else {
          // 返回所有客户端的host信息
          const allHostInfo = Object.entries(people).map(([id, client]) => ({
            sessionId: id,
            hostInfo: client.hostInfo
          }));
          console.log('获取所有客户端的Host信息:', allHostInfo);
          ws.send(
            JSON.stringify({
              type: "allHostInfo",
              data: allHostInfo
            })
          );
        }
        break;
    }
  });
});
