import WebSocket from "ws";
const WebSocketServer = WebSocket.Server;
const wss = new WebSocketServer({ port: 3002 });

function createId() {
  let e = () =>
    Math.floor((1 + Math.random()) * 65536)
      .toString(16)
      .substring(1);

  return `${e()}${e()}-${e()}-${e()}-${e()}-${e()}${e()}`;
}

//  存储连接的客户端
const people: People = {};

wss.on("connection", (ws) => {
  ws.on("message", (message) => {
    const msg: MsgDataType = JSON.parse(message.toString());

    switch (msg.type) {
      case "connect":
        // 将连接的客户端存起来
        const sessionId = createId();
        people[sessionId] = {
          sessionId,
          ws,
        };

        ws.send(JSON.stringify({ type: "content", data: sessionId }));
        break;
      case "call":
        // 将 sdp 发给接收端，sessionId 为 接收端的 id
        const sdp = msg.data.sdp;
        const sId = msg.data.sessionId;

        if (people[sId]) {
          people[sId].ws.send(JSON.stringify({ type: "call", data: sdp }));
        }
        break;
      case "answer":
        // 接收端将 sdp 发给发起端，sessionId 为 发起端的 id
        const answerSDP = msg.data.sdp;
        const recevId = msg.data.sessionId;

        if (people[recevId]) {
          people[recevId].ws.send(
            JSON.stringify({ type: "answer", data: answerSDP })
          );
        }
        break;
      case "getAllClients":
        ws.send(
          JSON.stringify({
            type: "getAllClients",
            data: Object.keys(people),
          })
        );
        break;
    }
  });
});
